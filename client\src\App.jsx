import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import ProtectedRoute, { PublicRoute, TeacherRoute, StudentRoute } from './components/ProtectedRoutes';
import Navbar from './components/Navbar';

// Pages
import Home from './pages/Home';
import Login from './pages/Login';
import Signup from './pages/Signup';
import Quizzes from './pages/Quizzes';

// Components
import CreateQuiz from './components/CreateQuiz';
import AttemptQuiz from './components/attemptQuiz';
import Result from './components/result';

import './App.css';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Navbar />
          <main className="main-content">
            <Routes>
              {/* Public routes - only accessible when not logged in */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />
              <Route
                path="/signup"
                element={
                  <PublicRoute>
                    <Signup />
                  </PublicRoute>
                }
              />

              {/* Protected routes - require authentication */}
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Home />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/quizzes"
                element={
                  <ProtectedRoute>
                    <Quizzes />
                  </ProtectedRoute>
                }
              />

              {/* Teacher-only routes */}
              <Route
                path="/create-quiz"
                element={
                  <TeacherRoute>
                    <CreateQuiz />
                  </TeacherRoute>
                }
              />

              {/* Student-only routes */}
              <Route
                path="/quiz/:id/attempt"
                element={
                  <StudentRoute>
                    <AttemptQuiz />
                  </StudentRoute>
                }
              />

              {/* Routes accessible by both roles */}
              <Route
                path="/quiz/:id/result"
                element={
                  <ProtectedRoute>
                    <Result />
                  </ProtectedRoute>
                }
              />

              {/* Catch all route - redirect to home */}
              <Route
                path="*"
                element={
                  <ProtectedRoute>
                    <Home />
                  </ProtectedRoute>
                }
              />
            </Routes>
          </main>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
