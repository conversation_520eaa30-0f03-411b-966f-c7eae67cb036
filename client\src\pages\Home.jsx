import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { quizAPI } from '../services/api';

const Home = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalQuizzes: 0,
    createdQuizzes: 0,
    participatedQuizzes: 0,
    recentQuizzes: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const response = await quizAPI.getAllQuizzes();

      if (response.success) {
        const allQuizzes = response.quizzes;

        // Calculate stats based on user role
        const userStats = {
          totalQuizzes: allQuizzes.length,
          recentQuizzes: allQuizzes.slice(0, 5) // Show 5 most recent
        };

        if (user.role === 'Teacher') {
          userStats.createdQuizzes = allQuizzes.filter(
            quiz => quiz.teacher._id === user.id
          ).length;
        } else {
          userStats.participatedQuizzes = allQuizzes.filter(
            quiz => quiz.responses?.some(response => response.student === user.id)
          ).length;
        }

        setStats(userStats);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading dashboard...</div>
      </div>
    );
  }

  return (
    <div className="home-container">
      <div className="welcome-section">
        <h1>Welcome back, {user?.name}!</h1>
        <p className="user-role-badge">Role: {user?.role}</p>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <h3>Total Quizzes</h3>
          <div className="stat-number">{stats.totalQuizzes}</div>
          <p>Available on platform</p>
        </div>

        {user?.role === 'Teacher' ? (
          <div className="stat-card">
            <h3>Created Quizzes</h3>
            <div className="stat-number">{stats.createdQuizzes}</div>
            <p>Quizzes you've created</p>
          </div>
        ) : (
          <div className="stat-card">
            <h3>Participated</h3>
            <div className="stat-number">{stats.participatedQuizzes}</div>
            <p>Quizzes you've taken</p>
          </div>
        )}

        <div className="stat-card">
          <h3>Recent Activity</h3>
          <div className="stat-number">{stats.recentQuizzes.length}</div>
          <p>Recent quizzes</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/quizzes" className="action-btn primary">
            View All Quizzes
          </Link>

          {user?.role === 'Teacher' && (
            <Link to="/create-quiz" className="action-btn secondary">
              Create New Quiz
            </Link>
          )}
        </div>
      </div>

      {/* Recent Quizzes */}
      {stats.recentQuizzes.length > 0 && (
        <div className="recent-quizzes">
          <h2>Recent Quizzes</h2>
          <div className="quiz-list">
            {stats.recentQuizzes.map((quiz) => (
              <div key={quiz._id} className="quiz-card">
                <div className="quiz-info">
                  <h3>{quiz.title}</h3>
                  <p>By: {quiz.teacher.name}</p>
                  <p>Questions: {quiz.questions?.length || 0}</p>
                  <p>Status: {quiz.isOpen ? 'Open' : 'Closed'}</p>
                </div>
                <div className="quiz-actions">
                  {user?.role === 'Student' && quiz.isOpen && (
                    <Link
                      to={`/quiz/${quiz._id}/attempt`}
                      className="btn btn-primary"
                    >
                      Take Quiz
                    </Link>
                  )}
                  <Link
                    to={`/quizzes`}
                    className="btn btn-secondary"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Role-specific content */}
      {user?.role === 'Teacher' ? (
        <div className="teacher-section">
          <h2>Teacher Dashboard</h2>
          <p>Manage your quizzes, view student responses, and create new assessments.</p>
          <div className="teacher-tips">
            <h3>Tips:</h3>
            <ul>
              <li>Create engaging questions with clear answer options</li>
              <li>Set appropriate marks for each question</li>
              <li>Open quizzes when students are ready to take them</li>
              <li>Review student responses to gauge understanding</li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="student-section">
          <h2>Student Dashboard</h2>
          <p>Take quizzes, view your results, and track your progress.</p>
          <div className="student-tips">
            <h3>Tips:</h3>
            <ul>
              <li>Read questions carefully before answering</li>
              <li>Review all options before selecting your answer</li>
              <li>Check your answers before submitting</li>
              <li>Learn from your results to improve</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default Home;
