import axios from 'axios';

// Configure axios defaults
const API_BASE_URL = 'http://localhost:5000';
axios.defaults.baseURL = API_BASE_URL;
axios.defaults.withCredentials = true;

// Request interceptor to add token to requests
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API functions
export const authAPI = {
  login: async (email, password) => {
    try {
      const response = await axios.post('/login', { email, password });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Login failed' };
    }
  },

  signup: async (name, email, password, role) => {
    try {
      const response = await axios.post('/signup', { name, email, password, role });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Signup failed' };
    }
  }
};

// Quiz API functions
export const quizAPI = {
  // Get all quizzes
  getAllQuizzes: async () => {
    try {
      const response = await axios.get('/quiz');
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch quizzes' };
    }
  },

  // Get single quiz by ID
  getQuiz: async (quizId) => {
    try {
      const response = await axios.get(`/quiz/${quizId}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to fetch quiz' };
    }
  },

  // Create new quiz (Teacher only)
  createQuiz: async (quizData) => {
    try {
      const response = await axios.post('/quiz/create', quizData);
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to create quiz' };
    }
  },

  // Submit quiz answers (Student only)
  submitQuiz: async (quizId, answers) => {
    try {
      const response = await axios.post(`/quiz/${quizId}/submit`, { answers });
      return response.data;
    } catch (error) {
      throw error.response?.data || { message: 'Failed to submit quiz' };
    }
  }
};

// Generic API helper functions
export const apiHelpers = {
  // Handle API errors consistently
  handleError: (error) => {
    if (error.response) {
      // Server responded with error status
      return error.response.data.message || 'Server error occurred';
    } else if (error.request) {
      // Request was made but no response received
      return 'Network error - please check your connection';
    } else {
      // Something else happened
      return error.message || 'An unexpected error occurred';
    }
  },

  // Format quiz data for display
  formatQuizData: (quiz) => {
    return {
      ...quiz,
      createdAt: new Date(quiz.createdAt).toLocaleDateString(),
      updatedAt: new Date(quiz.updatedAt).toLocaleDateString(),
      questionCount: quiz.questions?.length || 0,
      totalMarks: quiz.questions?.reduce((sum, q) => sum + (q.marks || 0), 0) || 0
    };
  },

  // Validate quiz form data
  validateQuizData: (quizData) => {
    const errors = [];
    
    if (!quizData.title?.trim()) {
      errors.push('Quiz title is required');
    }
    
    if (!quizData.questions || quizData.questions.length === 0) {
      errors.push('At least one question is required');
    }
    
    quizData.questions?.forEach((question, index) => {
      if (!question.title?.trim()) {
        errors.push(`Question ${index + 1}: Title is required`);
      }
      
      if (!question.answer || question.answer.length < 2) {
        errors.push(`Question ${index + 1}: At least 2 answer options are required`);
      }
      
      if (question.correctIndex === undefined || question.correctIndex < 0) {
        errors.push(`Question ${index + 1}: Correct answer must be selected`);
      }
      
      if (!question.marks || question.marks <= 0) {
        errors.push(`Question ${index + 1}: Marks must be greater than 0`);
      }
    });
    
    return errors;
  }
};

export default { authAPI, quizAPI, apiHelpers };
