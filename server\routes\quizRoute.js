const express = require("express");
const router = express.Router();
const { isAuth, isStudent, isTeacher } = require("../middlewares/auth");
const quizCtrl = require("../controllers/quizController");

router.post("/create", isAuth, isTeacher, quizCtrl.createQuiz);

router.post("/:id/submit", isAuth, isStudent, quizCtrl.submitQuiz);

router.get("/", isAuth, quizCtrl.getAllQuiz);

router.get("/:id", isAuth, quizCtrl.getQuiz);

module.exports = router;