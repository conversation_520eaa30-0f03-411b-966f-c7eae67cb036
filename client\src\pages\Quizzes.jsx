import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { quizAPI, apiHelpers } from '../services/api';

const Quizzes = () => {
  const { user } = useAuth();
  const [quizzes, setQuizzes] = useState([]);
  const [filteredQuizzes, setFilteredQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all'); // all, open, closed, mine
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchQuizzes();
  }, []);

  useEffect(() => {
    filterQuizzes();
  }, [quizzes, filter, searchTerm, user]);

  const fetchQuizzes = async () => {
    try {
      setLoading(true);
      const response = await quizAPI.getAllQuizzes();

      if (response.success) {
        const formattedQuizzes = response.quizzes.map(quiz =>
          apiHelpers.formatQuizData(quiz)
        );
        setQuizzes(formattedQuizzes);
      } else {
        setError('Failed to fetch quizzes');
      }
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      setError(apiHelpers.handleError(error));
    } finally {
      setLoading(false);
    }
  };

  const filterQuizzes = () => {
    let filtered = [...quizzes];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(quiz =>
        quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        quiz.teacher.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status/ownership filter
    switch (filter) {
      case 'open':
        filtered = filtered.filter(quiz => quiz.isOpen);
        break;
      case 'closed':
        filtered = filtered.filter(quiz => !quiz.isOpen);
        break;
      case 'mine':
        if (user.role === 'Teacher') {
          filtered = filtered.filter(quiz => quiz.teacher._id === user.id);
        } else {
          // For students, show quizzes they've participated in
          filtered = filtered.filter(quiz =>
            quiz.responses?.some(response => response.student === user.id)
          );
        }
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    setFilteredQuizzes(filtered);
  };

  const handleFilterChange = (newFilter) => {
    setFilter(newFilter);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading quizzes...</div>
      </div>
    );
  }

  return (
    <div className="quizzes-container">
      <div className="quizzes-header">
        <h1>Quizzes</h1>
        {user?.role === 'Teacher' && (
          <Link to="/create-quiz" className="btn btn-primary">
            Create New Quiz
          </Link>
        )}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Filters and Search */}
      <div className="quizzes-controls">
        <div className="search-box">
          <input
            type="text"
            placeholder="Search quizzes..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
          />
        </div>

        <div className="filter-buttons">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => handleFilterChange('all')}
          >
            All Quizzes
          </button>
          <button
            className={`filter-btn ${filter === 'open' ? 'active' : ''}`}
            onClick={() => handleFilterChange('open')}
          >
            Open
          </button>
          <button
            className={`filter-btn ${filter === 'closed' ? 'active' : ''}`}
            onClick={() => handleFilterChange('closed')}
          >
            Closed
          </button>
          <button
            className={`filter-btn ${filter === 'mine' ? 'active' : ''}`}
            onClick={() => handleFilterChange('mine')}
          >
            {user?.role === 'Teacher' ? 'My Quizzes' : 'Participated'}
          </button>
        </div>
      </div>

      {/* Quiz List */}
      <div className="quizzes-grid">
        {filteredQuizzes.length === 0 ? (
          <div className="no-quizzes">
            <h3>No quizzes found</h3>
            <p>
              {searchTerm || filter !== 'all'
                ? 'Try adjusting your search or filters'
                : 'No quizzes available at the moment'
              }
            </p>
          </div>
        ) : (
          filteredQuizzes.map((quiz) => (
            <QuizCard
              key={quiz._id}
              quiz={quiz}
              user={user}
              onRefresh={fetchQuizzes}
            />
          ))
        )}
      </div>
    </div>
  );
};

// Quiz Card Component
const QuizCard = ({ quiz, user, onRefresh }) => {
  const [submitting, setSubmitting] = useState(false);

  const hasParticipated = quiz.responses?.some(
    response => response.student === user.id
  );

  const getUserScore = () => {
    const userResponse = quiz.responses?.find(
      response => response.student === user.id
    );
    return userResponse ? userResponse.totalScore : null;
  };

  const handleQuizAction = async (action) => {
    // This would handle actions like opening/closing quiz for teachers
    // For now, just refresh the list
    onRefresh();
  };

  return (
    <div className="quiz-card">
      <div className="quiz-card-header">
        <h3>{quiz.title}</h3>
        <span className={`status-badge ${quiz.isOpen ? 'open' : 'closed'}`}>
          {quiz.isOpen ? 'Open' : 'Closed'}
        </span>
      </div>

      <div className="quiz-card-body">
        <div className="quiz-info">
          <p><strong>Created by:</strong> {quiz.teacher.name}</p>
          <p><strong>Questions:</strong> {quiz.questionCount}</p>
          <p><strong>Total Marks:</strong> {quiz.totalMarks}</p>
          <p><strong>Created:</strong> {quiz.createdAt}</p>
        </div>

        {user.role === 'Student' && hasParticipated && (
          <div className="participation-info">
            <p><strong>Your Score:</strong> {getUserScore()}/{quiz.totalMarks}</p>
            <p className="participated-badge">✓ Completed</p>
          </div>
        )}

        {user.role === 'Teacher' && quiz.teacher._id === user.id && (
          <div className="teacher-info">
            <p><strong>Responses:</strong> {quiz.responses?.length || 0}</p>
          </div>
        )}
      </div>

      <div className="quiz-card-actions">
        {user.role === 'Student' ? (
          <>
            {quiz.isOpen && !hasParticipated ? (
              <Link
                to={`/quiz/${quiz._id}/attempt`}
                className="btn btn-primary"
              >
                Take Quiz
              </Link>
            ) : hasParticipated ? (
              <Link
                to={`/quiz/${quiz._id}/result`}
                className="btn btn-secondary"
              >
                View Result
              </Link>
            ) : (
              <button className="btn btn-disabled" disabled>
                Quiz Closed
              </button>
            )}
          </>
        ) : (
          // Teacher actions
          <>
            <Link
              to={`/quiz/${quiz._id}/result`}
              className="btn btn-secondary"
            >
              View Results
            </Link>
            {quiz.teacher._id === user.id && (
              <button
                className="btn btn-outline"
                onClick={() => handleQuizAction('toggle')}
                disabled={submitting}
              >
                {quiz.isOpen ? 'Close Quiz' : 'Open Quiz'}
              </button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Quizzes;
