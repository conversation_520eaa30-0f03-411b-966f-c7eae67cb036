const express = require("express");
const app = express();
require("dotenv").config();

const cookieParser = require("cookie-parser");

app.use(express.json());
app.use(cookieParser());

const dbConnect = require("./config/database");
dbConnect();

const auth = require("./routes/authRoutes");
app.use("/",auth);


const quiz = require("./routes/quizRoute");
app.use("/quiz", quiz);

const PORT = process.env.PORT;

app.listen(PORT,()=>{
  console.log(`Server is listening on ${PORT}`);
})