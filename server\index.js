const express = require("express");
const app = express();
require("dotenv").config();


const cors = require("cors");
const cookieParser = require("cookie-parser");

app.use(cors());
app.use(express.json());
app.use(cookieParser());

const dbConnect = require("./config/database");
dbConnect();

const auth = require("./routes/authRoutes");
app.use("/",auth);


const quiz = require("./routes/quizRoute");
app.use("/quiz", quiz);

const PORT = process.env.PORT || 5000;

app.listen(PORT,()=>{
  console.log(`Server is listening on ${PORT}`);
})