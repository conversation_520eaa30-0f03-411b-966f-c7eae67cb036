import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { quizAPI, apiHelpers } from '../services/api';
import { useAuth } from '../context/AuthContext';

const Result = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  const [quiz, setQuiz] = useState(null);
  const [userResult, setUserResult] = useState(null);
  const [allResults, setAllResults] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [viewMode, setViewMode] = useState('summary'); // summary, detailed, all-results

  useEffect(() => {
    fetchQuizResults();
  }, [id]);

  const fetchQuizResults = async () => {
    try {
      setLoading(true);
      const response = await quizAPI.getQuiz(id);

      if (response.success) {
        const quizData = response.quiz;
        setQuiz(quizData);

        // Find user's result
        const userResponse = quizData.responses?.find(
          response => response.student === user.id
        );

        if (userResponse) {
          setUserResult(userResponse);
        } else if (user.role === 'Student') {
          setError('You have not taken this quiz yet');
          return;
        }

        // For teachers, show all results
        if (user.role === 'Teacher') {
          setAllResults(quizData.responses || []);
        }

        // If coming from quiz submission, use the passed score
        if (location.state?.score !== undefined) {
          setUserResult(prev => ({
            ...prev,
            totalScore: location.state.score
          }));
        }
      } else {
        setError('Failed to load quiz results');
      }
    } catch (error) {
      console.error('Error fetching quiz results:', error);
      setError(apiHelpers.handleError(error));
    } finally {
      setLoading(false);
    }
  };

  const calculatePercentage = (score, maxScore) => {
    return maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;
  };

  const getGrade = (percentage) => {
    if (percentage >= 90) return 'A';
    if (percentage >= 80) return 'B';
    if (percentage >= 70) return 'C';
    if (percentage >= 60) return 'D';
    return 'F';
  };

  const getTotalMarks = () => {
    return quiz?.questions?.reduce((sum, q) => sum + (q.marks || 0), 0) || 0;
  };

  const getQuestionResult = (questionIndex) => {
    if (!userResult || !quiz) return null;

    const question = quiz.questions[questionIndex];
    const userAnswer = userResult.answers[questionIndex];
    const isCorrect = userAnswer === question.correctIndex;

    return {
      question,
      userAnswer,
      correctAnswer: question.correctIndex,
      isCorrect,
      marksEarned: isCorrect ? question.marks : 0
    };
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading results...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Unable to Load Results</h2>
          <p>{error}</p>
          <button
            onClick={() => navigate('/quizzes')}
            className="btn btn-primary"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  if (!quiz) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Quiz Not Found</h2>
          <button
            onClick={() => navigate('/quizzes')}
            className="btn btn-primary"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  const totalMarks = getTotalMarks();
  const userScore = userResult?.totalScore || 0;
  const percentage = calculatePercentage(userScore, totalMarks);
  const grade = getGrade(percentage);

  return (
    <div className="result-container">
      {/* Header */}
      <div className="result-header">
        <h1>Quiz Results</h1>
        <h2>{quiz.title}</h2>
        <p>Created by: {quiz.teacher.name}</p>
      </div>

      {/* Navigation Tabs */}
      <div className="result-tabs">
        <button
          className={`tab ${viewMode === 'summary' ? 'active' : ''}`}
          onClick={() => setViewMode('summary')}
        >
          Summary
        </button>
        {userResult && (
          <button
            className={`tab ${viewMode === 'detailed' ? 'active' : ''}`}
            onClick={() => setViewMode('detailed')}
          >
            Detailed Review
          </button>
        )}
        {user.role === 'Teacher' && (
          <button
            className={`tab ${viewMode === 'all-results' ? 'active' : ''}`}
            onClick={() => setViewMode('all-results')}
          >
            All Results
          </button>
        )}
      </div>

      {/* Summary View */}
      {viewMode === 'summary' && (
        <div className="result-summary">
          {userResult ? (
            <div className="score-card">
              <div className="score-display">
                <div className="score-number">
                  {userScore}/{totalMarks}
                </div>
                <div className="score-percentage">
                  {percentage}%
                </div>
                <div className="score-grade">
                  Grade: {grade}
                </div>
              </div>

              <div className="score-details">
                <div className="detail-item">
                  <span>Questions Answered:</span>
                  <span>{quiz.questions.length}</span>
                </div>
                <div className="detail-item">
                  <span>Correct Answers:</span>
                  <span>
                    {quiz.questions.filter((_, index) => {
                      const result = getQuestionResult(index);
                      return result?.isCorrect;
                    }).length}
                  </span>
                </div>
                <div className="detail-item">
                  <span>Submission Time:</span>
                  <span>
                    {userResult.submittedAt
                      ? new Date(userResult.submittedAt).toLocaleString()
                      : 'Just now'
                    }
                  </span>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-result">
              <h3>No Results Available</h3>
              <p>
                {user.role === 'Student'
                  ? 'You have not taken this quiz yet.'
                  : 'No students have taken this quiz yet.'
                }
              </p>
            </div>
          )}

          <div className="quiz-stats">
            <h3>Quiz Statistics</h3>
            <div className="stats-grid">
              <div className="stat-item">
                <span>Total Questions:</span>
                <span>{quiz.questions.length}</span>
              </div>
              <div className="stat-item">
                <span>Total Marks:</span>
                <span>{totalMarks}</span>
              </div>
              <div className="stat-item">
                <span>Total Responses:</span>
                <span>{quiz.responses?.length || 0}</span>
              </div>
              <div className="stat-item">
                <span>Quiz Status:</span>
                <span className={quiz.isOpen ? 'status-open' : 'status-closed'}>
                  {quiz.isOpen ? 'Open' : 'Closed'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed Review */}
      {viewMode === 'detailed' && userResult && (
        <div className="detailed-review">
          <h3>Question by Question Review</h3>
          {quiz.questions.map((question, index) => {
            const result = getQuestionResult(index);
            return (
              <div key={index} className="question-review">
                <div className="question-header">
                  <h4>Question {index + 1}</h4>
                  <span className={`result-badge ${result.isCorrect ? 'correct' : 'incorrect'}`}>
                    {result.isCorrect ? 'Correct' : 'Incorrect'}
                  </span>
                  <span className="marks">
                    {result.marksEarned}/{question.marks} marks
                  </span>
                </div>

                <div className="question-text">
                  <p>{question.title}</p>
                </div>

                <div className="answer-review">
                  {question.answer.map((option, optionIndex) => (
                    <div
                      key={optionIndex}
                      className={`answer-option ${
                        optionIndex === question.correctIndex ? 'correct-answer' : ''
                      } ${
                        optionIndex === result.userAnswer ? 'user-answer' : ''
                      }`}
                    >
                      <span className="option-text">{option}</span>
                      {optionIndex === question.correctIndex && (
                        <span className="label correct-label">Correct Answer</span>
                      )}
                      {optionIndex === result.userAnswer && (
                        <span className="label user-label">Your Answer</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* All Results (Teacher View) */}
      {viewMode === 'all-results' && user.role === 'Teacher' && (
        <div className="all-results">
          <h3>All Student Results</h3>
          {allResults.length === 0 ? (
            <p>No students have taken this quiz yet.</p>
          ) : (
            <div className="results-table">
              <table>
                <thead>
                  <tr>
                    <th>Student</th>
                    <th>Score</th>
                    <th>Percentage</th>
                    <th>Grade</th>
                    <th>Submitted At</th>
                  </tr>
                </thead>
                <tbody>
                  {allResults.map((result, index) => {
                    const resultPercentage = calculatePercentage(result.totalScore, totalMarks);
                    const resultGrade = getGrade(resultPercentage);

                    return (
                      <tr key={index}>
                        <td>{result.student?.name || 'Unknown Student'}</td>
                        <td>{result.totalScore}/{totalMarks}</td>
                        <td>{resultPercentage}%</td>
                        <td className={`grade grade-${resultGrade.toLowerCase()}`}>
                          {resultGrade}
                        </td>
                        <td>
                          {result.submittedAt
                            ? new Date(result.submittedAt).toLocaleString()
                            : 'N/A'
                          }
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="result-actions">
        <button
          onClick={() => navigate('/quizzes')}
          className="btn btn-secondary"
        >
          Back to Quizzes
        </button>

        {user.role === 'Student' && quiz.isOpen && !userResult && (
          <button
            onClick={() => navigate(`/quiz/${id}/attempt`)}
            className="btn btn-primary"
          >
            Take Quiz
          </button>
        )}
      </div>
    </div>
  );
};

export default Result;
