import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { quizAPI, apiHelpers } from '../services/api';

const CreateQuiz = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState([]);

  const [quizData, setQuizData] = useState({
    title: '',
    isOpen: false,
    questions: [
      {
        title: '',
        answer: ['', ''],
        correctIndex: 0,
        marks: 1
      }
    ]
  });

  const handleQuizChange = (field, value) => {
    setQuizData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleQuestionChange = (questionIndex, field, value) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((question, index) =>
        index === questionIndex
          ? { ...question, [field]: value }
          : question
      )
    }));
  };

  const handleAnswerChange = (questionIndex, answerIndex, value) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((question, qIndex) =>
        qIndex === questionIndex
          ? {
              ...question,
              answer: question.answer.map((answer, aIndex) =>
                aIndex === answerIndex ? value : answer
              )
            }
          : question
      )
    }));
  };

  const addQuestion = () => {
    setQuizData(prev => ({
      ...prev,
      questions: [
        ...prev.questions,
        {
          title: '',
          answer: ['', ''],
          correctIndex: 0,
          marks: 1
        }
      ]
    }));
  };

  const removeQuestion = (questionIndex) => {
    if (quizData.questions.length > 1) {
      setQuizData(prev => ({
        ...prev,
        questions: prev.questions.filter((_, index) => index !== questionIndex)
      }));
    }
  };

  const addAnswer = (questionIndex) => {
    setQuizData(prev => ({
      ...prev,
      questions: prev.questions.map((question, index) =>
        index === questionIndex
          ? { ...question, answer: [...question.answer, ''] }
          : question
      )
    }));
  };

  const removeAnswer = (questionIndex, answerIndex) => {
    const question = quizData.questions[questionIndex];
    if (question.answer.length > 2) {
      setQuizData(prev => ({
        ...prev,
        questions: prev.questions.map((q, qIndex) =>
          qIndex === questionIndex
            ? {
                ...q,
                answer: q.answer.filter((_, aIndex) => aIndex !== answerIndex),
                correctIndex: q.correctIndex >= answerIndex && q.correctIndex > 0
                  ? q.correctIndex - 1
                  : q.correctIndex
              }
            : q
        )
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const validationErrors = apiHelpers.validateQuizData(quizData);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setLoading(true);
    setErrors([]);

    try {
      const response = await quizAPI.createQuiz(quizData);

      if (response.success) {
        navigate('/quizzes');
      } else {
        setErrors([response.message || 'Failed to create quiz']);
      }
    } catch (error) {
      console.error('Error creating quiz:', error);
      setErrors([apiHelpers.handleError(error)]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="create-quiz-container">
      <div className="create-quiz-header">
        <h1>Create New Quiz</h1>
        <button
          type="button"
          onClick={() => navigate('/quizzes')}
          className="btn btn-secondary"
        >
          Cancel
        </button>
      </div>

      {errors.length > 0 && (
        <div className="error-message">
          <h4>Please fix the following errors:</h4>
          <ul>
            {errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <form onSubmit={handleSubmit} className="create-quiz-form">
        {/* Quiz Basic Info */}
        <div className="quiz-basic-info">
          <div className="form-group">
            <label htmlFor="title">Quiz Title *</label>
            <input
              type="text"
              id="title"
              value={quizData.title}
              onChange={(e) => handleQuizChange('title', e.target.value)}
              placeholder="Enter quiz title"
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={quizData.isOpen}
                onChange={(e) => handleQuizChange('isOpen', e.target.checked)}
              />
              Open quiz immediately after creation
            </label>
          </div>
        </div>

        {/* Questions */}
        <div className="questions-section">
          <div className="questions-header">
            <h2>Questions</h2>
            <button
              type="button"
              onClick={addQuestion}
              className="btn btn-outline"
            >
              Add Question
            </button>
          </div>

          {quizData.questions.map((question, questionIndex) => (
            <QuestionForm
              key={questionIndex}
              question={question}
              questionIndex={questionIndex}
              onQuestionChange={handleQuestionChange}
              onAnswerChange={handleAnswerChange}
              onAddAnswer={addAnswer}
              onRemoveAnswer={removeAnswer}
              onRemoveQuestion={removeQuestion}
              canRemoveQuestion={quizData.questions.length > 1}
            />
          ))}
        </div>

        {/* Submit Button */}
        <div className="form-actions">
          <button
            type="submit"
            disabled={loading}
            className="btn btn-primary btn-large"
          >
            {loading ? 'Creating Quiz...' : 'Create Quiz'}
          </button>
        </div>
      </form>
    </div>
  );
};

// Question Form Component
const QuestionForm = ({
  question,
  questionIndex,
  onQuestionChange,
  onAnswerChange,
  onAddAnswer,
  onRemoveAnswer,
  onRemoveQuestion,
  canRemoveQuestion
}) => {
  return (
    <div className="question-form">
      <div className="question-header">
        <h3>Question {questionIndex + 1}</h3>
        {canRemoveQuestion && (
          <button
            type="button"
            onClick={() => onRemoveQuestion(questionIndex)}
            className="btn btn-danger btn-small"
          >
            Remove Question
          </button>
        )}
      </div>

      <div className="form-group">
        <label htmlFor={`question-title-${questionIndex}`}>Question Text *</label>
        <textarea
          id={`question-title-${questionIndex}`}
          value={question.title}
          onChange={(e) => onQuestionChange(questionIndex, 'title', e.target.value)}
          placeholder="Enter your question"
          className="form-textarea"
          rows="3"
        />
      </div>

      <div className="form-group">
        <label htmlFor={`question-marks-${questionIndex}`}>Marks *</label>
        <input
          type="number"
          id={`question-marks-${questionIndex}`}
          value={question.marks}
          onChange={(e) => onQuestionChange(questionIndex, 'marks', parseInt(e.target.value) || 1)}
          min="1"
          className="form-input marks-input"
        />
      </div>

      <div className="answers-section">
        <div className="answers-header">
          <label>Answer Options *</label>
          <button
            type="button"
            onClick={() => onAddAnswer(questionIndex)}
            className="btn btn-outline btn-small"
          >
            Add Option
          </button>
        </div>

        {question.answer.map((answer, answerIndex) => (
          <div key={answerIndex} className="answer-option">
            <div className="answer-input-group">
              <label className="radio-label">
                <input
                  type="radio"
                  name={`correct-answer-${questionIndex}`}
                  checked={question.correctIndex === answerIndex}
                  onChange={() => onQuestionChange(questionIndex, 'correctIndex', answerIndex)}
                />
                <span className="radio-text">Correct</span>
              </label>

              <input
                type="text"
                value={answer}
                onChange={(e) => onAnswerChange(questionIndex, answerIndex, e.target.value)}
                placeholder={`Option ${answerIndex + 1}`}
                className="form-input answer-input"
              />

              {question.answer.length > 2 && (
                <button
                  type="button"
                  onClick={() => onRemoveAnswer(questionIndex, answerIndex)}
                  className="btn btn-danger btn-small"
                >
                  Remove
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CreateQuiz;
