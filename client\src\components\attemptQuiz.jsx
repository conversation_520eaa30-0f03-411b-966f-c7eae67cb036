import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { quizAPI, apiHelpers } from '../services/api';
import { useAuth } from '../context/AuthContext';

const AttemptQuiz = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [quiz, setQuiz] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState([]);
  const [timeRemaining, setTimeRemaining] = useState(null);
  const [showConfirmSubmit, setShowConfirmSubmit] = useState(false);

  useEffect(() => {
    fetchQuiz();
  }, [id]);

  useEffect(() => {
    // Initialize answers array when quiz is loaded
    if (quiz) {
      setAnswers(new Array(quiz.questions.length).fill(-1));
    }
  }, [quiz]);

  const fetchQuiz = async () => {
    try {
      setLoading(true);
      const response = await quizAPI.getQuiz(id);

      if (response.success) {
        const quizData = response.quiz;

        // Check if quiz is open
        if (!quizData.isOpen) {
          setError('This quiz is currently closed');
          return;
        }

        // Check if user has already participated
        const hasParticipated = quizData.responses?.some(
          response => response.student === user.id
        );

        if (hasParticipated) {
          setError('You have already taken this quiz');
          return;
        }

        setQuiz(quizData);
      } else {
        setError('Failed to load quiz');
      }
    } catch (error) {
      console.error('Error fetching quiz:', error);
      setError(apiHelpers.handleError(error));
    } finally {
      setLoading(false);
    }
  };

  const handleAnswerSelect = (answerIndex) => {
    setAnswers(prev => {
      const newAnswers = [...prev];
      newAnswers[currentQuestionIndex] = answerIndex;
      return newAnswers;
    });
  };

  const goToQuestion = (questionIndex) => {
    setCurrentQuestionIndex(questionIndex);
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmitQuiz = async () => {
    setSubmitting(true);

    try {
      const response = await quizAPI.submitQuiz(id, answers);

      if (response.success) {
        navigate(`/quiz/${id}/result`, {
          state: {
            score: response.data.totalScore,
            maxScore: response.data.maxScore
          }
        });
      } else {
        setError(response.message || 'Failed to submit quiz');
      }
    } catch (error) {
      console.error('Error submitting quiz:', error);
      setError(apiHelpers.handleError(error));
    } finally {
      setSubmitting(false);
      setShowConfirmSubmit(false);
    }
  };

  const getAnsweredCount = () => {
    return answers.filter(answer => answer !== -1).length;
  };

  const getUnansweredQuestions = () => {
    return answers
      .map((answer, index) => answer === -1 ? index + 1 : null)
      .filter(q => q !== null);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner">Loading quiz...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Unable to Load Quiz</h2>
          <p>{error}</p>
          <button
            onClick={() => navigate('/quizzes')}
            className="btn btn-primary"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  if (!quiz) {
    return (
      <div className="error-container">
        <div className="error-message">
          <h2>Quiz Not Found</h2>
          <button
            onClick={() => navigate('/quizzes')}
            className="btn btn-primary"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const unansweredQuestions = getUnansweredQuestions();

  return (
    <div className="attempt-quiz-container">
      {/* Quiz Header */}
      <div className="quiz-header">
        <div className="quiz-info">
          <h1>{quiz.title}</h1>
          <p>Created by: {quiz.teacher.name}</p>
        </div>
        <div className="quiz-progress">
          <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
          <span>Answered: {getAnsweredCount()}/{quiz.questions.length}</span>
        </div>
      </div>

      {/* Question Navigation */}
      <div className="question-navigation">
        <div className="question-numbers">
          {quiz.questions.map((_, index) => (
            <button
              key={index}
              onClick={() => goToQuestion(index)}
              className={`question-number ${
                index === currentQuestionIndex ? 'current' : ''
              } ${answers[index] !== -1 ? 'answered' : 'unanswered'}`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>

      {/* Current Question */}
      <div className="question-container">
        <div className="question-header">
          <h2>Question {currentQuestionIndex + 1}</h2>
          <span className="question-marks">Marks: {currentQuestion.marks}</span>
        </div>

        <div className="question-text">
          <p>{currentQuestion.title}</p>
        </div>

        <div className="answer-options">
          {currentQuestion.answer.map((option, index) => (
            <label key={index} className="answer-option">
              <input
                type="radio"
                name={`question-${currentQuestionIndex}`}
                value={index}
                checked={answers[currentQuestionIndex] === index}
                onChange={() => handleAnswerSelect(index)}
              />
              <span className="answer-text">{option}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Navigation Buttons */}
      <div className="quiz-navigation">
        <button
          onClick={goToPreviousQuestion}
          disabled={currentQuestionIndex === 0}
          className="btn btn-secondary"
        >
          Previous
        </button>

        <div className="nav-center">
          <button
            onClick={() => setShowConfirmSubmit(true)}
            className="btn btn-primary"
          >
            Submit Quiz
          </button>
        </div>

        <button
          onClick={goToNextQuestion}
          disabled={currentQuestionIndex === quiz.questions.length - 1}
          className="btn btn-secondary"
        >
          Next
        </button>
      </div>

      {/* Submit Confirmation Modal */}
      {showConfirmSubmit && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>Submit Quiz?</h3>
            <p>Are you sure you want to submit your quiz?</p>

            {unansweredQuestions.length > 0 && (
              <div className="warning">
                <p><strong>Warning:</strong> You have {unansweredQuestions.length} unanswered questions:</p>
                <p>Questions: {unansweredQuestions.join(', ')}</p>
              </div>
            )}

            <div className="modal-actions">
              <button
                onClick={() => setShowConfirmSubmit(false)}
                className="btn btn-secondary"
                disabled={submitting}
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitQuiz}
                className="btn btn-primary"
                disabled={submitting}
              >
                {submitting ? 'Submitting...' : 'Submit Quiz'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttemptQuiz;
